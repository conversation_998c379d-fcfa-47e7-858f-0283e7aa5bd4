class HumanCounter {
    constructor() {
        this.video = document.getElementById('video');
        this.canvas = document.getElementById('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.loading = document.getElementById('loading');
        
        // UI elements
        this.startBtn = document.getElementById('startBtn');
        this.stopBtn = document.getElementById('stopBtn');
        this.toggleDetectionBtn = document.getElementById('toggleDetection');
        this.currentCountEl = document.getElementById('currentCount');
        this.maxCountEl = document.getElementById('maxCount');
        this.fpsEl = document.getElementById('fps');
        this.confidenceSlider = document.getElementById('confidenceSlider');
        this.confidenceValue = document.getElementById('confidenceValue');
        this.showBoxesCheckbox = document.getElementById('showBoxes');
        this.showLabelsCheckbox = document.getElementById('showLabels');
        
        // State
        this.model = null;
        this.stream = null;
        this.isDetecting = false;
        this.currentCount = 0;
        this.maxCount = 0;
        this.confidence = 0.5;
        this.showBoxes = true;
        this.showLabels = true;
        
        // Performance tracking
        this.lastTime = 0;
        this.frameCount = 0;
        this.fps = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadModel();
    }
    
    setupEventListeners() {
        this.startBtn.addEventListener('click', () => this.startCamera());
        this.stopBtn.addEventListener('click', () => this.stopCamera());
        this.toggleDetectionBtn.addEventListener('click', () => this.toggleDetection());
        
        this.confidenceSlider.addEventListener('input', (e) => {
            this.confidence = parseFloat(e.target.value);
            this.confidenceValue.textContent = this.confidence;
        });
        
        this.showBoxesCheckbox.addEventListener('change', (e) => {
            this.showBoxes = e.target.checked;
        });
        
        this.showLabelsCheckbox.addEventListener('change', (e) => {
            this.showLabels = e.target.checked;
        });
    }
    
    async loadModel() {
        try {
            console.log('Loading COCO-SSD model...');
            this.model = await cocoSsd.load();
            console.log('Model loaded successfully');
            this.loading.style.display = 'none';

            // Auto-start detection if camera is already running
            if (this.stream && this.video.videoWidth > 0 && !this.isDetecting) {
                setTimeout(() => {
                    this.startDetection();
                }, 500);
            }
        } catch (error) {
            console.error('Error loading model:', error);
            this.loading.innerHTML = '<p style="color: red;">Error loading AI model. Please refresh the page.</p>';
        }
    }
    
    async startCamera() {
        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user'
                }
            });

            this.video.srcObject = this.stream;

            this.video.onloadedmetadata = () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;

                // Auto-start detection when camera is ready and model is loaded
                if (this.model && !this.isDetecting) {
                    setTimeout(() => {
                        this.startDetection();
                    }, 500); // Small delay to ensure video is fully ready
                }
            };

            this.startBtn.disabled = true;
            this.stopBtn.disabled = false;
            this.toggleDetectionBtn.disabled = false;

        } catch (error) {
            console.error('Error accessing camera:', error);
            alert('Error accessing camera. Please make sure you have granted camera permissions.');
        }
    }
    
    stopCamera() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        this.video.srcObject = null;
        this.stopDetection();

        this.startBtn.disabled = false;
        this.stopBtn.disabled = true;
        this.toggleDetectionBtn.disabled = true;
        this.toggleDetectionBtn.textContent = 'Start Detection';
        this.toggleDetectionBtn.classList.remove('btn-secondary');
        this.toggleDetectionBtn.classList.add('btn-accent');

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.updateCount(0);
        this.resetStats();
    }
    
    toggleDetection() {
        if (!this.model) {
            alert('AI model is still loading. Please wait.');
            return;
        }

        if (this.isDetecting) {
            this.stopDetection();
        } else {
            this.startDetection();
        }
    }

    startDetection() {
        if (!this.model) {
            console.log('Model not ready yet');
            return;
        }

        this.isDetecting = true;
        this.toggleDetectionBtn.textContent = 'Stop Detection';
        this.toggleDetectionBtn.classList.remove('btn-accent');
        this.toggleDetectionBtn.classList.add('btn-secondary');
        this.detectLoop();
        console.log('Detection started');
    }

    stopDetection() {
        this.isDetecting = false;
        this.toggleDetectionBtn.textContent = 'Start Detection';
        this.toggleDetectionBtn.classList.remove('btn-secondary');
        this.toggleDetectionBtn.classList.add('btn-accent');
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.updateCount(0);
        console.log('Detection stopped');
    }
    
    async detectLoop() {
        if (!this.isDetecting || !this.model) return;
        
        try {
            const predictions = await this.model.detect(this.video);
            this.processPredictions(predictions);
            this.updateFPS();
        } catch (error) {
            console.error('Detection error:', error);
        }
        
        if (this.isDetecting) {
            requestAnimationFrame(() => this.detectLoop());
        }
    }
    
    processPredictions(predictions) {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Filter for people with confidence above threshold
        const people = predictions.filter(prediction => 
            prediction.class === 'person' && prediction.score >= this.confidence
        );
        
        this.updateCount(people.length);
        
        // Draw bounding boxes and labels
        if (this.showBoxes || this.showLabels) {
            this.drawDetections(people);
        }
    }
    
    drawDetections(people) {
        people.forEach((person, index) => {
            const [x, y, width, height] = person.bbox;
            
            if (this.showBoxes) {
                // Draw bounding box
                this.ctx.strokeStyle = '#00ff00';
                this.ctx.lineWidth = 3;
                this.ctx.strokeRect(x, y, width, height);
                
                // Draw corner markers
                const cornerSize = 20;
                this.ctx.fillStyle = '#00ff00';
                
                // Top-left corner
                this.ctx.fillRect(x, y, cornerSize, 3);
                this.ctx.fillRect(x, y, 3, cornerSize);
                
                // Top-right corner
                this.ctx.fillRect(x + width - cornerSize, y, cornerSize, 3);
                this.ctx.fillRect(x + width - 3, y, 3, cornerSize);
                
                // Bottom-left corner
                this.ctx.fillRect(x, y + height - 3, cornerSize, 3);
                this.ctx.fillRect(x, y + height - cornerSize, 3, cornerSize);
                
                // Bottom-right corner
                this.ctx.fillRect(x + width - cornerSize, y + height - 3, cornerSize, 3);
                this.ctx.fillRect(x + width - 3, y + height - cornerSize, 3, cornerSize);
            }
            
            if (this.showLabels) {
                // Draw label background
                const label = `Person ${index + 1} (${Math.round(person.score * 100)}%)`;
                this.ctx.font = '16px Arial';
                const textWidth = this.ctx.measureText(label).width;
                
                this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
                this.ctx.fillRect(x, y - 25, textWidth + 10, 25);
                
                // Draw label text
                this.ctx.fillStyle = 'black';
                this.ctx.fillText(label, x + 5, y - 8);
            }
        });
    }
    
    updateCount(count) {
        this.currentCount = count;
        this.currentCountEl.textContent = count;
        
        if (count > this.maxCount) {
            this.maxCount = count;
            this.maxCountEl.textContent = this.maxCount;
        }
    }
    
    updateFPS() {
        const now = performance.now();
        this.frameCount++;

        if (now - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (now - this.lastTime));
            this.fpsEl.textContent = this.fps;
            this.frameCount = 0;
            this.lastTime = now;
        }
    }

    resetStats() {
        this.currentCount = 0;
        this.maxCount = 0;
        this.fps = 0;
        this.frameCount = 0;
        this.lastTime = 0;

        this.currentCountEl.textContent = '0';
        this.maxCountEl.textContent = '0';
        this.fpsEl.textContent = '0';
    }
}

// Initialize the application when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new HumanCounter();
});
