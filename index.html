<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Human Counter</title>
    <link rel="stylesheet" href="styles.css">
    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    <!-- COCO-SSD Model -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@2.2.2/dist/coco-ssd.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 AI Human Counter</h1>
            <p>Real-time human detection and counting using your camera</p>
        </header>

        <main>
            <div class="video-container">
                <video id="video" autoplay muted playsinline></video>
                <canvas id="canvas"></canvas>
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Loading AI model...</p>
                </div>
            </div>

            <div class="controls">
                <button id="startBtn" class="btn btn-primary">Start Camera</button>
                <button id="stopBtn" class="btn btn-secondary" disabled>Stop Camera</button>
                <button id="toggleDetection" class="btn btn-accent" disabled>Start Detection</button>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="currentCount">0</div>
                    <div class="stat-label">People Detected</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="maxCount">0</div>
                    <div class="stat-label">Maximum Count</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="fps">0</div>
                    <div class="stat-label">FPS</div>
                </div>
            </div>

            <div class="settings">
                <h3>Settings</h3>
                <div class="setting-group">
                    <label for="confidenceSlider">Detection Confidence: <span id="confidenceValue">0.5</span></label>
                    <input type="range" id="confidenceSlider" min="0.1" max="1" step="0.1" value="0.5">
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="showBoxes" checked>
                        Show Detection Boxes
                    </label>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="showLabels" checked>
                        Show Labels
                    </label>
                </div>
            </div>

            <div class="info">
                <h3>How it works</h3>
                <ul>
                    <li>🎥 Uses your device camera to capture video</li>
                    <li>🧠 Runs TensorFlow.js COCO-SSD model in your browser</li>
                    <li>👥 Detects and counts people in real-time</li>
                    <li>🔒 All processing happens locally - no data sent to servers</li>
                </ul>
            </div>
        </main>

        <footer>
            <p>Built with TensorFlow.js • Privacy-first AI</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
