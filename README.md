# AI Human Counter Web Application

A real-time human detection and counting web application that uses your device's camera and TensorFlow.js with the COCO-SSD model to detect and count people in the video feed.

## Features

- 🎥 **Real-time Camera Access**: Uses your device's camera for live video feed
- 🧠 **AI-Powered Detection**: Utilizes TensorFlow.js and COCO-SSD model for accurate human detection
- 👥 **Live Counting**: Counts people in real-time with visual feedback
- 🎯 **Adjustable Confidence**: Customize detection sensitivity
- 📊 **Performance Metrics**: Shows FPS and detection statistics
- 🔒 **Privacy-First**: All processing happens locally in your browser
- 📱 **Responsive Design**: Works on desktop and mobile devices

## How to Use

1. **Start the Application**:
   ```bash
   python3 -m http.server 8000
   ```
   Then open http://localhost:8000 in your browser

2. **Grant Camera Permission**: 
   - Click "Start Camera" and allow camera access when prompted

3. **Begin Detection**:
   - Click "Start Detection" to begin AI-powered human counting
   - The application will draw bounding boxes around detected people
   - View real-time count and statistics

4. **Customize Settings**:
   - Adjust detection confidence threshold (0.1 - 1.0)
   - Toggle detection boxes and labels on/off
   - Monitor FPS and maximum count reached

## Technical Details

### Technologies Used
- **HTML5**: Structure and video elements
- **CSS3**: Modern responsive styling with gradients and animations
- **JavaScript ES6+**: Application logic and camera integration
- **TensorFlow.js**: Machine learning framework
- **COCO-SSD Model**: Pre-trained object detection model

### Browser Requirements
- Modern browser with WebRTC support (Chrome, Firefox, Safari, Edge)
- Camera access permissions
- JavaScript enabled

### Performance Optimization
- Efficient canvas rendering
- Optimized detection loop using requestAnimationFrame
- Configurable confidence thresholds to balance accuracy vs performance
- FPS monitoring for performance tracking

## File Structure

```
├── index.html          # Main HTML structure
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript application logic
└── README.md           # This documentation
```

## Key Features Explained

### Human Detection
- Uses COCO-SSD model to detect "person" class objects
- Filters detections based on confidence threshold
- Real-time processing of video frames

### Visual Feedback
- Green bounding boxes around detected people
- Corner markers for better visibility
- Confidence percentage labels
- Real-time count display

### Privacy & Security
- All AI processing happens locally in the browser
- No video data is sent to external servers
- Camera access is controlled by browser permissions

## Troubleshooting

**Camera not working?**
- Ensure camera permissions are granted
- Check if camera is being used by another application
- Try refreshing the page

**AI model not loading?**
- Check internet connection (model downloads from CDN)
- Ensure JavaScript is enabled
- Try refreshing the page

**Poor detection accuracy?**
- Adjust confidence threshold in settings
- Ensure good lighting conditions
- Make sure people are clearly visible in frame

## Browser Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## License

This project is open source and available under the MIT License.
